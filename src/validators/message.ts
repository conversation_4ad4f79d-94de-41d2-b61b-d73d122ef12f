import { z } from 'zod/v4';

/**
 * 获取消息列表的查询参数验证
 */
export const getMessagesQuerySchema = z.object({
    /** 分页大小，默认20，最大100 */
    limit: z
        .string()
        .optional()
        .transform(val => (val !== undefined && val.trim() !== '' ? parseInt(val, 10) : 20))
        .refine(val => val > 0 && val <= 100, {
            message: 'limit must be between 1 and 100',
        }),

    /** 游标，用于分页，传入上一页最后一条消息的创建时间 */
    cursor: z
        .string()
        .optional()
        .refine(val => val === undefined || val === '' || !isNaN(Date.parse(val)), {
            message: 'cursor must be a valid ISO date string',
        }),
});

/**
 * 消息ID参数验证
 */
export const messageIdParamSchema = z.object({
    id: z.uuid(),
});

/**
 * 用户消息统计查询参数验证
 */
export const getMessageStatsQuerySchema = z.object({
    /** 统计时间范围，可选：7d, 30d, 90d, all，默认30d */
    timeRange: z.enum(['7d', '30d', '90d', 'all']).optional().default('30d'),
    /** 是否包含详细统计，默认false */
    includeDetails: z
        .string()
        .optional()
        .transform(val => val === 'true'),
});

/**
 * 会话ID参数验证
 */
export const sessionIdParamSchema = z.object({
    sessionId: z.uuid(),
});

/**
 * A2U MessageContent 验证
 * 对应数据库schema中的MessageContent类型，支持text、file、data三种类型
 */
export const messageContentSchema = z.object({
    type: z.enum(['text', 'file', 'data']),
    // text类型
    text: z.string().optional(),
    // file类型
    file: z
        .object({
            bytes: z.string().optional(), // base64编码的文件内容
            uri: z.string().optional(), // 文件URI地址
            metadata: z.record(z.string(), z.unknown()).optional(),
        })
        .optional(),
    // data类型 - 支持对象数组或字符串
    data: z
        .union([
            z.string(), // 字符串形式的数据
            z.array(z.record(z.string(), z.unknown())), // 对象数组形式的数据
        ])
        .optional(),
    metadata: z.record(z.string(), z.unknown()).optional(),
});
/**
 * 消息发送者验证
 */
export const senderSchema = z
    .object({
        id: z.string().min(1, 'Sender ID is required'),
        name: z.string().optional(),
        avatar: z.url().optional(),
        type: z.string().optional(),
    })
    .catchall(z.any());

/**
 * 创建单条消息验证
 */
export const createMessageSchema = z.object({
    id: z.string().min(1, 'Message ID is required'),
    role: z.enum(['user', 'assistant'], {
        error: 'Role must be either "user" or "assistant"',
    }),
    content: z.array(messageContentSchema).min(1, 'At least one content item is required'),
    sender: senderSchema.optional(),
    sessionId: z.uuid(),
    userId: z.string().optional(),
});

/**
 * 批量创建消息验证
 */
export const createMessagesSchema = z.object({
    messages: z
        .array(createMessageSchema)
        .min(1, 'At least one message is required')
        .max(100, 'Cannot create more than 100 messages at once'),
});

export type GetMessagesQuery = z.infer<typeof getMessagesQuerySchema>;
export type MessageIdParam = z.infer<typeof messageIdParamSchema>;
export type GetMessageStatsQuery = z.infer<typeof getMessageStatsQuerySchema>;
export type SessionIdParam = z.infer<typeof sessionIdParamSchema>;
export type MessageContent = z.infer<typeof messageContentSchema>;
export type Sender = z.infer<typeof senderSchema>;
export type CreateMessage = z.infer<typeof createMessageSchema>;
export type CreateMessages = z.infer<typeof createMessagesSchema>;
